// Map Integration JavaScript - Global variables
let currentView = 'employees';
let mapInitialized = false;
let selectedDepartment = null;
// Note: employeesData is provided by script.js via window.employeesData

// Global functions for view switching - properly exposed to window object
function switchToEmployees() {
    console.log('Switching to employees view');
    currentView = 'employees';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        employeeListBtn.classList.add('active');
        officeMapBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Seznam zaměstnanců';

        // Switch sections
        mapSection.style.display = 'none';
        employeeSection.style.display = 'block';

        // Remove map view class from body
        document.body.classList.remove('map-view');

        console.log('Successfully switched to employees view');
    } else {
        console.error('Some elements not found for employees view switch');
    }
}

function switchToMap() {
    console.log('Switching to map view');
    currentView = 'map';

    const employeeListBtn = document.getElementById('employeeListBtn');
    const officeMapBtn = document.getElementById('officeMapBtn');
    const employeeSection = document.getElementById('employeeSection');
    const mapSection = document.getElementById('mapSection');
    const pageTitle = document.getElementById('pageTitle');

    if (employeeListBtn && officeMapBtn && employeeSection && mapSection && pageTitle) {
        // Update buttons
        officeMapBtn.classList.add('active');
        employeeListBtn.classList.remove('active');

        // Update title
        pageTitle.textContent = 'Mapa rozmístění pracovišť';

        // Switch sections
        employeeSection.style.display = 'none';
        mapSection.style.display = 'block';

        // Add map view class to body
        document.body.classList.add('map-view');

        // Initialize departments panel
        initializeDepartmentsPanel();

        // Initialize map on first display
        if (!mapInitialized) {
            console.log('Initializing map for first time');
            if (typeof window.initializeMap === 'function') {
                window.initializeMap();
                mapInitialized = true;
            } else {
                console.log('Waiting for map initialization...');
                // Wait for initializeMap to be available
                const checkInitialize = setInterval(() => {
                    if (typeof window.initializeMap === 'function') {
                        clearInterval(checkInitialize);
                        window.initializeMap();
                        mapInitialized = true;
                    }
                }, 50);
            }
        }

        console.log('Successfully switched to map view');
    } else {
        console.error('Some elements not found for map view switch');
    }
}

// Expose functions to global scope for onclick handlers
window.switchToEmployees = switchToEmployees;
window.switchToMap = switchToMap;

document.addEventListener('DOMContentLoaded', function() {
    console.log('Map integration script loaded!');
    console.log('window.employeesData available:', typeof window.employeesData !== 'undefined');
    if (typeof window.employeesData !== 'undefined') {
        console.log('window.employeesData length:', window.employeesData.length);
    }

    // Wait a bit for DOM to fully load
    setTimeout(function() {
        console.log('Initializing map integration...');
        initializeMapIntegration();
    }, 100);
});

function initializeMapIntegration() {
    console.log('initializeMapIntegration started');
    console.log('window.employeesData check:', typeof window.employeesData !== 'undefined');

    // Pozice markerů se nyní načítají přímo ze zaměstnanců (left a top vlastnosti)

    // Load employee data from global variable
    function loadEmployeesData() {
        return new Promise((resolve) => {
            function checkData() {
                if (typeof window.employeesData !== 'undefined' && window.employeesData.length > 0) {
                    console.log('Employee data loaded:', window.employeesData.length, 'employees');
                    resolve();
                } else {
                    console.log('Waiting for employee data...');
                    setTimeout(checkData, 100);
                }
            }
            checkData();
        });
    }

    // Funkce pro normalizaci jmen
    function normalizeName(str) {
        return str
            .normalize('NFD')
            .replace(/\p{Diacritic}/gu, '')
            .toLowerCase()
            .replace(/\s+/g, ' ')
            .trim();
    }

    // Detekce pohlaví podle jména
    function detectGender(fullName) {
        const nameParts = fullName.split(' ');
        const firstName = nameParts[nameParts.length - 1].toLowerCase();

        const femaleNames = [
            'alena', 'andrea', 'beáta', 'beata', 'eva', 'hana', 'helena', 'jana', 'jiřina', 'jirina',
            'kateřina', 'katerina', 'katarína', 'katarina', 'lucie', 'magdalena', 'markéta', 'marketa',
            'martina', 'michaela', 'monika', 'pavla', 'petra', 'soňa', 'sona', 'veronika', 'zuzana',
            'žaneta', 'zaneta', 'alice', 'varvara', 'magda', 'katarína', 'fridrichová', 'staňková',
            'stašková', 'soukupová', 'procházková', 'kurfiřtová', 'vachalová', 'erhartová', 'hulová',
            'jedličková', 'horová', 'nohejlová', 'mesteková', 'mácová', 'mnuková', 'nardelli', 'zelenková',
            'zezuľáková', 'karolová', 'maňurová', 'vichrová', 'vlčková', 'hrdá', 'gabriel', 'kopecká',
            'sojka', 'pešková'
        ];

        const hasFemaleLastName = fullName.toLowerCase().includes('ová') ||
            (fullName.toLowerCase().endsWith('ina') && !fullName.toLowerCase().includes('ák') && !fullName.toLowerCase().includes('ak'));

        const specialFemaleNames = [
            'vasjuňkina varvara', 'hrdá veronika', 'gabriel martina', 'kopecká zuzana',
            'nardelli magdalena', 'sojka alena', 'pešková monika'
        ];

        const isSpecialFemale = specialFemaleNames.includes(fullName.toLowerCase());

        const maleNames = [
            'petr', 'tomáš', 'tomas', 'ondřej', 'ondrej', 'jakub', 'jindřich', 'jindrich', 'jaroslav',
            'boris', 'libor', 'václav', 'vaclav', 'filip', 'vojtěch', 'vojtech', 'pavel', 'michal',
            'karel', 'jaromír', 'jaromir', 'roman', 'miroslav', 'zdeněk', 'zdenek', 'dušan', 'dusan',
            'josef', 'jiří', 'jiri', 'stanislav', 'igor', 'akaki', 'zbyněk', 'zbyněk', 'kryštof',
            'krystof', 'martin', 'ondřej', 'ondrej', 'pavel', 'lajos', 'františek', 'frantisek'
        ];

        const isMale = maleNames.includes(firstName);
        const isFemale = femaleNames.includes(firstName) || hasFemaleLastName || isSpecialFemale;

        if (isMale) {
            return 'male';
        } else if (isFemale) {
            return 'female';
        } else {
            return 'male'; // default
        }
    }

    // Pozice markerů se nyní načítají přímo ze zaměstnanců - funkce není potřeba



    // Inicializace mapy
    function initializeMap() {
        console.log('Inicializace mapy...');
        console.log('Inicializace s', window.employeesData.length, 'zaměstnanci');
        
        renderEmployeeList(window.employeesData);
        renderMarkers(window.employeesData);
        setupMapSearch();
        setupMapToggle();

        // Kontrola načtení obrázku
        const mapImg = document.getElementById('office-map-img');
        const errorDiv = document.getElementById('map-error');

        mapImg.onload = function() {
            errorDiv.style.display = 'none';
        };

        mapImg.onerror = function() {
            errorDiv.style.display = 'block';
        };
    }

    // Make initializeMap globally accessible
    window.initializeMap = initializeMap;

    // Vytvoření horizontálního seznamu zaměstnanců - přesná kopie z mapa.html
    function renderEmployeeList(list) {
        const ul = document.getElementById('mapEmployeeList');
        if (!ul) {
            console.error('Element mapEmployeeList nebyl nalezen');
            return;
        }

        console.log('Rendering employee list, počet:', list ? list.length : 'undefined');
        console.log('List data:', list);

        // Fade out effect
        ul.style.opacity = '0.5';
        ul.style.transform = 'translateY(10px)';

        setTimeout(() => {
            ul.innerHTML = '';

            console.log('Vytvářím elementy pro', list.length, 'zaměstnanců');
            list.forEach((z, index) => {
                const li = document.createElement('li');
                li.tabIndex = 0;
                li.dataset.jmeno = z.jmeno;

                const avatar = document.createElement('img');
                avatar.src = z.obrazek || 'img/no-person-photo.png';
                avatar.alt = z.jmeno;
                avatar.className = 'avatar-img';
                avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
                li.appendChild(avatar);

                const infoContainer = document.createElement('div');
                infoContainer.className = 'employee-info';

                const nameElement = document.createElement('h3');
                const nameParts = z.jmeno.split(' ');
                if (nameParts.length >= 2) {
                    nameElement.innerHTML = nameParts[0] + '<br>' + nameParts.slice(1).join(' ');
                } else {
                    nameElement.textContent = z.jmeno;
                }
                nameElement.className = 'emp-name';
                nameElement.title = z.jmeno;
                infoContainer.appendChild(nameElement);

                li.appendChild(infoContainer);

                const btn = document.createElement('button');
                btn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Najít';
                btn.className = 'find-btn';
                btn.title = 'Zobrazit na mapě';
                btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
                li.appendChild(btn);

                li.onclick = e => {
                    e.preventDefault();
                    highlightByName(z.jmeno, true);
                };

                li.addEventListener('keydown', e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        highlightByName(z.jmeno, true);
                    }
                });

                li.style.opacity = '0';
                li.style.transform = 'translateX(-20px)';
                ul.appendChild(li);
                console.log('Přidán element pro:', z.jmeno);

                setTimeout(() => {
                    li.style.transition = 'all 0.3s ease';
                    li.style.opacity = '1';
                    li.style.transform = 'translateX(0)';
                }, index * 50);
            });

            setTimeout(() => {
                ul.style.transition = 'all 0.3s ease';
                ul.style.opacity = '1';
                ul.style.transform = 'translateY(0)';
                console.log('Seznam zaměstnanců vykreslen, počet elementů:', ul.children.length);
            }, 100);
        }, 150);
    }

    // Vytvoření markerů na mapě - používá pozice ze zaměstnanců
    function renderMarkers(list) {
        const container = document.querySelector('.office-map-container');
        if (!container) {
            console.error('Map container not found');
            return;
        }

        container.querySelectorAll('.marker').forEach(m => m.remove());

        // Prochází všechny zaměstnance a vytváří markery pro ty, kteří mají pozice
        let markersCreated = 0;
        list.forEach((employee) => {
            // Kontroluje, zda má zaměstnanec definované pozice
            if (!employee.left || !employee.top) {
                console.log(`Zaměstnanec ${employee.jmeno} nemá definované pozice na mapě`);
                return; // Přeskočí zaměstnance bez pozice
            }
            markersCreated++;

            const marker = document.createElement('div');

            const genderType = detectGender(employee.jmeno);
            marker.className = `marker ${genderType}`;

            marker.style.top = employee.top + 'px';
            marker.style.left = employee.left + 'px';
            marker.dataset.jmeno = employee.jmeno;
            marker.tabIndex = 0;

            const genderDot = document.createElement('div');
            genderDot.style.width = '26px';
            genderDot.style.height = '26px';
            genderDot.style.borderRadius = '50%';
            genderDot.style.background = genderType === 'female' ? '#ec4899' : '#3b82f6';
            genderDot.style.boxShadow = genderType === 'female' ?
                '0 2px 8px rgba(236, 72, 153, 0.3)' :
                '0 2px 8px rgba(59, 130, 246, 0.3)';
            genderDot.style.border = '2.5px solid #fff';
            genderDot.style.display = 'block';
            genderDot.style.transition = 'all 0.3s ease';
            genderDot.title = `${employee.jmeno} (${genderType === 'female' ? 'Žena' : 'Muž'})`;
            marker.appendChild(genderDot);

            const tooltip = document.createElement('span');
            tooltip.className = 'tooltip';
            tooltip.innerHTML = employee.jmeno;
            marker.appendChild(tooltip);

            marker.addEventListener('click', () => {
                highlightByName(employee.jmeno, true);
            });

            container.appendChild(marker);
        });

        console.log(`Vytvořeno markerů: ${markersCreated} z ${list.length} zaměstnanců`);
        console.log('Markery na mapě:', container.querySelectorAll('.marker').length);
    }

    // Zvýraznění podle jména - přesná kopie z mapa.html
    function highlightByName(jmeno, scrollTo) {
        // Vyčištění všech zvýraznění
        clearAllHighlights();

        // Najít a zvýraznit marker
        const activeMarker = findAndHighlightMarker(jmeno, scrollTo);

        // Najít a zvýraznit zaměstnance
        const activeEmployee = findAndHighlightEmployee(jmeno, scrollTo);

        // Zobrazit animaci nalezení
        if (activeEmployee || activeMarker) {
            showFoundAnimation(jmeno);
        }
    }

    // Vyčištění všech zvýraznění
    function clearAllHighlights() {
        document.querySelectorAll('.marker').forEach(marker => {
            marker.classList.remove('active', 'found-pulse');
        });
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            li.classList.remove('active', 'found-highlight');
        });
    }

    // Najít a zvýraznit marker
    function findAndHighlightMarker(jmeno, scrollTo) {
        let foundMarker = null;
        document.querySelectorAll('.marker').forEach(marker => {
            const isActive = marker.dataset.jmeno === jmeno;
            if (isActive) {
                foundMarker = marker;
                marker.classList.add('active', 'found-pulse');

                if (scrollTo) {
                    setTimeout(() => {
                        marker.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'center'
                        });

                        setTimeout(() => {
                            marker.classList.add('extra-pulse');
                            setTimeout(() => marker.classList.remove('extra-pulse'), 2000);
                        }, 800);
                    }, 300);
                }
            }
        });
        return foundMarker;
    }

    // Najít a zvýraznit zaměstnance v seznamu
    function findAndHighlightEmployee(jmeno, scrollTo) {
        let foundEmployee = null;
        document.querySelectorAll('#mapEmployeeList li').forEach(li => {
            const isActive = li.dataset.jmeno === jmeno;
            if (isActive) {
                foundEmployee = li;
                li.classList.add('active', 'found-highlight');

                if (scrollTo) {
                    setTimeout(() => {
                        li.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest',
                            inline: 'nearest'
                        });
                    }, 100);
                }
            }
        });
        return foundEmployee;
    }

    // Animace nalezení
    function showFoundAnimation(jmeno) {
        console.log('Nalezen:', jmeno);
        // Zde můžeme přidat další animace podle potřeby
    }

    // Vyhledávání na mapě - přesná kopie z mapa.html
    function setupMapSearch() {
        const searchInput = document.getElementById('mapSearchInput');
        const searchClearBtn = document.getElementById('mapSearchClearBtn');
        let searchTimeout;

        if (!searchInput || !searchClearBtn) {
            console.log('Search elements not found');
            return;
        }

        function updateClearButton() {
            if (searchInput.value.trim()) {
                searchClearBtn.classList.add('visible');
            } else {
                searchClearBtn.classList.remove('visible');
            }
        }

        function updateResultsCount(count, isSearching) {
            const resultsCount = document.getElementById('mapSearchResultsCount');
            const resultsText = document.getElementById('mapResultsCountText');

            if (resultsCount && resultsText) {
                if (isSearching) {
                    resultsText.textContent = count === 1 ? '1 výsledek' : `${count} výsledků`;
                    resultsCount.classList.add('visible');
                } else {
                    resultsCount.classList.remove('visible');
                }
            }
        }

        searchInput.addEventListener('input', function() {
            const val = this.value.toLowerCase().trim();
            updateClearButton();

            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                if (val === '') {
                    renderEmployeeList(window.employeesData);
                    updateResultsCount(window.employeesData.length, false);
                } else {
                    const filtered = window.employeesData.filter(z => {
                        const name = normalizeName(z.jmeno);
                        const searchTerm = normalizeName(val);
                        return name.includes(searchTerm);
                    });

                    renderEmployeeList(filtered);
                    updateResultsCount(filtered.length, true);

                    if (filtered.length === 1) {
                        setTimeout(() => {
                            highlightByName(filtered[0].jmeno, true);
                        }, 500);
                    } else if (filtered.length === 0 && val.length > 2) {
                        // Show not found message for longer search terms
                        showNotFoundMessage(val);
                    }
                }
            }, 200);
        });

        searchClearBtn.addEventListener('click', function() {
            searchInput.value = '';
            updateClearButton();
            renderEmployeeList(window.employeesData);
            updateResultsCount(window.employeesData.length, false);
            searchInput.focus();
        });
    }



    // Nastavení toggle tlačítek pro zobrazení - kopie z mapa.html
    function setupMapToggle() {
        const toggleBtns = document.querySelectorAll('.toggle-btn');
        const mapContainer = document.querySelector('.office-map-container');

        if (!toggleBtns.length || !mapContainer) {
            console.log('Toggle buttons or map container not found');
            return;
        }

        toggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.getAttribute('data-view');

                // Aktualizace aktivního tlačítka
                toggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Přepnutí režimu zobrazení
                if (view === 'single') {
                    mapContainer.classList.add('single-view');
                } else {
                    mapContainer.classList.remove('single-view');
                }

                console.log('Přepnuto na režim:', view);
            });
        });
    }

    // Globální funkce pro vyhledání zaměstnance na mapě - přesná kopie z mapa.html
    window.findEmployeeOnMap = function(employeeName) {
        if (currentView !== 'map') {
            switchToMap();
            setTimeout(() => findEmployeeOnMap(employeeName), 500);
            return;
        }

        highlightByName(employeeName, true);
    };

    // Zobrazení detailů zaměstnance (použije existující modal)
    function showEmployeeDetails(employee) {
        // Použije existující modal z hlavní stránky
        if (typeof window.openModal === 'function') {
            window.openModal(employee);
        }
    }

    // Navigační tlačítka nyní používají onclick atributy

    // Helper functions for search notifications - copied from mapa.html
    function showSearchHint(jmeno) {
        const hint = document.createElement('div');
        hint.className = 'search-hint';
        hint.innerHTML = `
            <i class="fas fa-lightbulb"></i>
            Automaticky nalezen: <strong>${jmeno}</strong>
        `;

        document.body.appendChild(hint);

        setTimeout(() => hint.classList.add('show'), 100);
        setTimeout(() => {
            hint.classList.remove('show');
            setTimeout(() => hint.remove(), 300);
        }, 2500);
    }

    function showNotFoundMessage(searchTerm) {
        const message = document.createElement('div');
        message.className = 'not-found-message';
        message.innerHTML = `
            <i class="fas fa-search"></i>
            Nenalezen zaměstnanec: "<strong>${searchTerm}</strong>"
        `;

        document.body.appendChild(message);

        setTimeout(() => message.classList.add('show'), 100);
        setTimeout(() => {
            message.classList.remove('show');
            setTimeout(() => message.remove(), 300);
        }, 2000);
    }

    // Inicializace
    loadEmployeesData().then(() => {
        console.log('Data načtena, inicializace dokončena');
        console.log('Počet zaměstnanců:', window.employeesData ? window.employeesData.length : 'undefined');
        // Nastavení výchozího zobrazení
        window.switchToEmployees();
    });

// Funkce pro inicializaci levého panelu s oddělením
function initializeDepartmentsPanel() {
    if (!window.employeesData) {
        console.log('Data zaměstnanců nejsou k dispozici');
        return;
    }

    const departmentsList = document.getElementById('departmentsList');
    if (!departmentsList) {
        console.log('Element departmentsList nenalezen');
        return;
    }

    // Získáme všechna oddělení a spočítáme zaměstnance
    const departments = {};
    window.employeesData.forEach(employee => {
        const dept = employee.oddeleni;
        if (!departments[dept]) {
            departments[dept] = [];
        }
        departments[dept].push(employee);
    });

    // Seřadíme oddělení podle názvu
    const sortedDepartments = Object.keys(departments).sort();

    // Vygenerujeme HTML
    departmentsList.innerHTML = '';

    // Přidáme "Všichni zaměstnanci"
    const allItem = document.createElement('div');
    allItem.className = 'department-item active';
    allItem.setAttribute('data-department', 'all');
    allItem.innerHTML = `
        <div class="department-name">Všichni zaměstnanci</div>
        <div class="department-count">${window.employeesData.length} zaměstnanců</div>
    `;
    allItem.addEventListener('click', () => selectDepartment('all', allItem));
    departmentsList.appendChild(allItem);

    // Přidáme jednotlivá oddělení
    sortedDepartments.forEach(dept => {
        const item = document.createElement('div');
        item.className = 'department-item';
        item.setAttribute('data-department', dept);
        item.innerHTML = `
            <div class="department-name">${dept}</div>
            <div class="department-count">${departments[dept].length} zaměstnanců</div>
        `;
        item.addEventListener('click', () => selectDepartment(dept, item));
        departmentsList.appendChild(item);
    });
}

// Funkce pro výběr oddělení
function selectDepartment(department, element) {
    // Odstraníme active třídu ze všech položek
    document.querySelectorAll('.department-item').forEach(item => {
        item.classList.remove('active');
    });

    // Přidáme active třídu k vybrané položce
    element.classList.add('active');

    // Uložíme vybrané oddělení
    selectedDepartment = department;

    // Zvýrazníme zaměstnance na mapě
    highlightDepartmentOnMap(department);
}

// Funkce pro zvýraznění oddělení na mapě
function highlightDepartmentOnMap(department) {
    const markers = document.querySelectorAll('.marker');

    markers.forEach(marker => {
        const employeeName = marker.getAttribute('data-jmeno');
        const employee = window.employeesData.find(emp => emp.jmeno === employeeName);

        if (department === 'all') {
            // Zobrazíme všechny zaměstnance
            marker.style.opacity = '1';
            marker.style.transform = 'scale(1)';
            marker.style.filter = 'none';
        } else if (employee && employee.oddeleni === department) {
            // Zvýrazníme zaměstnance z vybraného oddělení
            marker.style.opacity = '1';
            marker.style.transform = 'scale(1.2)';
            marker.style.filter = 'brightness(1.2) saturate(1.3)';
        } else {
            // Ztlumíme ostatní zaměstnance
            marker.style.opacity = '0.2';
            marker.style.transform = 'scale(0.8)';
            marker.style.filter = 'grayscale(0.8) brightness(0.6)';
        }
    });

    // Také filtrujeme seznam zaměstnanců
    filterEmployeeList(department);
}

// Funkce pro filtrování seznamu zaměstnanců podle oddělení
function filterEmployeeList(department) {
    const employeeList = document.getElementById('mapEmployeeList');
    if (!employeeList) return;

    const listItems = employeeList.querySelectorAll('li');

    listItems.forEach(item => {
        const employeeName = item.getAttribute('data-jmeno');
        const employee = window.employeesData.find(emp => emp.jmeno === employeeName);

        if (department === 'all') {
            item.style.display = 'flex';
            item.style.opacity = '1';
        } else if (employee && employee.oddeleni === department) {
            item.style.display = 'flex';
            item.style.opacity = '1';
        } else {
            item.style.display = 'none';
            item.style.opacity = '0.3';
        }
    });
}

// Funkce pro zvýraznění konkrétního zaměstnance (volaná z modálního okna)
function highlightEmployeeOnMap(employeeName) {
    // Najdeme zaměstnance
    const employee = window.employeesData.find(emp => emp.jmeno === employeeName);
    if (!employee) return;

    // Vyberme oddělení zaměstnance v levém panelu
    const departmentItem = document.querySelector(`[data-department="${employee.oddeleni}"]`);
    if (departmentItem) {
        selectDepartment(employee.oddeleni, departmentItem);
    }

    // Zvýrazníme konkrétního zaměstnance
    const marker = document.querySelector(`[data-jmeno="${employeeName}"]`);
    if (marker) {
        marker.style.transform = 'scale(1.5)';
        marker.style.zIndex = '1000';
        marker.style.filter = 'brightness(1.3) saturate(1.5)';
        marker.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Po chvilce vrátíme normální velikost
        setTimeout(() => {
            marker.style.transform = 'scale(1.2)';
            marker.style.zIndex = '';
            marker.style.filter = 'brightness(1.2) saturate(1.3)';
        }, 2000);
    }
}

// Zpřístupníme funkce globálně
window.initializeDepartmentsPanel = initializeDepartmentsPanel;
window.highlightEmployeeOnMap = highlightEmployeeOnMap;
}
